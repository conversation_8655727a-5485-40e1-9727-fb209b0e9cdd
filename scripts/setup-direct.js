#!/usr/bin/env node

/**
 * Direct Database Setup for WordPress to Supabase Migration
 * 
 * This script connects directly to PostgreSQL and executes the setup SQL
 */

import pg from 'pg';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: '.env.local' });

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Extract database connection details from Supabase URL
function getConnectionConfig() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  }

  // Extract project ID from URL
  const match = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/);
  if (!match) {
    throw new Error('Invalid Supabase URL format');
  }

  const projectId = match[1];
  
  return {
    host: `db.${projectId}.supabase.co`,
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: serviceKey.split('.')[2] // Extract password from service key
  };
}

// Setup database using direct PostgreSQL connection
async function setupDatabase() {
  info('Setting up database using direct PostgreSQL connection...');

  let client;
  
  try {
    // Get connection config
    const config = getConnectionConfig();
    
    // Create PostgreSQL client
    client = new pg.Client(config);
    
    info('Connecting to PostgreSQL...');
    await client.connect();
    success('Connected to PostgreSQL');

    // Read and execute SQL setup file
    const sqlPath = join(__dirname, 'setup-supabase.sql');
    const sqlContent = readFileSync(sqlPath, 'utf8');
    
    info('Executing setup SQL...');
    
    // Split SQL into statements and execute each one
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.toLowerCase().includes('select ') || !stmt.includes('message'));

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.toLowerCase().includes('select ') && statement.includes('message')) {
        continue; // Skip success message
      }
      
      try {
        await client.query(statement);
        info(`Executed statement ${i + 1}/${statements.length}`);
      } catch (err) {
        if (err.message.includes('already exists') || err.message.includes('does not exist')) {
          warning(`Statement ${i + 1}: ${err.message}`);
        } else {
          throw err;
        }
      }
    }

    success('Database setup completed successfully!');
    return true;

  } catch (err) {
    error(`Database setup failed: ${err.message}`);
    
    if (err.message.includes('password authentication failed')) {
      error('Authentication failed. Please check your SUPABASE_SERVICE_ROLE_KEY');
    } else if (err.message.includes('ENOTFOUND')) {
      error('Could not connect to database. Please check your NEXT_PUBLIC_SUPABASE_URL');
    }
    
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
}

// Test the setup
async function testSetup() {
  info('Testing database setup...');

  let client;
  
  try {
    const config = getConnectionConfig();
    client = new pg.Client(config);
    await client.connect();

    // Test migration_jobs table
    const jobsResult = await client.query('SELECT COUNT(*) FROM migration_jobs');
    success('migration_jobs table accessible');

    // Test migration functions
    const funcResult = await client.query("SELECT create_migration_tables('test')");
    if (funcResult.rows[0].create_migration_tables.success) {
      success('Migration functions working');
      
      // Clean up test tables
      await client.query('DROP TABLE IF EXISTS wp_posts_test');
      await client.query('DROP TABLE IF EXISTS wp_pages_test');
      await client.query('DROP TABLE IF EXISTS wp_media_test');
      info('Test tables cleaned up');
    } else {
      throw new Error('Migration function test failed');
    }

    return true;

  } catch (err) {
    error(`Setup test failed: ${err.message}`);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
}

// Main execution
async function main() {
  log(`${colors.bold}🏗️  WordPress to Supabase Migration - Direct Database Setup${colors.reset}\n`);

  // Setup database
  const setupSuccess = await setupDatabase();
  if (!setupSuccess) {
    error('\n❌ Database setup failed');
    info('\n📋 Alternative setup options:');
    info('1. Check your environment variables in .env.local');
    info('2. Run: npm run setup:manual');
    info('3. Or manually execute scripts/setup-supabase.sql in Supabase dashboard');
    process.exit(1);
  }

  // Test the setup
  const testSuccess = await testSetup();
  if (!testSuccess) {
    warning('Setup completed but tests failed. The app may still work.');
  }

  success('\n🎉 Database setup completed successfully!');
  log('\nNext steps:');
  log('1. Run: npm run dev');
  log('2. Open: http://localhost:3000');
  log('3. Test the WordPress connection');
  log('4. Start your migration!');
}

main().catch(err => {
  error(`Setup failed: ${err.message}`);
  process.exit(1);
});
