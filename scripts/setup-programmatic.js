#!/usr/bin/env node

/**
 * Programmatic Database Setup for WordPress to Supabase Migration
 * 
 * This script sets up the database using the Supabase Management API
 * without requiring manual SQL execution.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAccessToken = process.env.SUPABASE_ACCESS_TOKEN;

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Extract project ID from URL
function getProjectId() {
  const match = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/);
  return match ? match[1] : null;
}

// Execute SQL using Supabase Management API
async function executeSQL(sql) {
  const projectId = getProjectId();
  
  if (!projectId || !supabaseAccessToken) {
    throw new Error('Missing project ID or access token');
  }

  const response = await fetch(`https://api.supabase.com/v1/projects/${projectId}/database/query`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${supabaseAccessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query: sql })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`SQL execution failed: ${response.status} ${errorText}`);
  }

  return await response.json();
}

// Setup database programmatically
async function setupDatabase() {
  info('Setting up database programmatically...');

  try {
    // Create migration_jobs table
    info('Creating migration_jobs table...');
    
    const migrationJobsSQL = `
      CREATE TABLE IF NOT EXISTS migration_jobs (
        id SERIAL PRIMARY KEY,
        domain TEXT NOT NULL,
        suffix TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        progress JSONB DEFAULT '{}',
        total_items INTEGER DEFAULT 0,
        completed_items INTEGER DEFAULT 0,
        error_log TEXT[],
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE,
        last_checkpoint JSONB DEFAULT '{}'
      );

      ALTER TABLE migration_jobs ENABLE ROW LEVEL SECURITY;

      DROP POLICY IF EXISTS "migration_jobs_service_policy" ON migration_jobs;
      CREATE POLICY "migration_jobs_service_policy" ON migration_jobs
        FOR ALL TO service_role
        USING (true)
        WITH CHECK (true);

      DROP POLICY IF EXISTS "migration_jobs_read_policy" ON migration_jobs;
      CREATE POLICY "migration_jobs_read_policy" ON migration_jobs
        FOR SELECT TO authenticated
        USING (true);
    `;

    await executeSQL(migrationJobsSQL);
    success('migration_jobs table created');

    // Create migration functions
    info('Creating migration functions...');
    
    const functionsSQL = `
      CREATE OR REPLACE FUNCTION create_migration_tables(table_suffix TEXT)
      RETURNS JSONB AS $$
      DECLARE
        result JSONB := '{}';
      BEGIN
        -- Create wp_posts table
        EXECUTE format('
          CREATE TABLE IF NOT EXISTS wp_posts_%s (
            id SERIAL PRIMARY KEY,
            wp_id INTEGER NOT NULL,
            title TEXT,
            content TEXT,
            excerpt TEXT,
            slug TEXT,
            status TEXT,
            type TEXT,
            author_id INTEGER,
            featured_media_id INTEGER,
            categories TEXT[],
            tags TEXT[],
            created_at TIMESTAMP WITH TIME ZONE,
            modified_at TIMESTAMP WITH TIME ZONE,
            migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )', table_suffix);

        -- Create wp_pages table
        EXECUTE format('
          CREATE TABLE IF NOT EXISTS wp_pages_%s (
            id SERIAL PRIMARY KEY,
            wp_id INTEGER NOT NULL,
            title TEXT,
            content TEXT,
            slug TEXT,
            status TEXT,
            parent_id INTEGER,
            template TEXT,
            featured_media_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE,
            modified_at TIMESTAMP WITH TIME ZONE,
            migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )', table_suffix);

        -- Create wp_media table
        EXECUTE format('
          CREATE TABLE IF NOT EXISTS wp_media_%s (
            id SERIAL PRIMARY KEY,
            wp_id INTEGER NOT NULL,
            title TEXT,
            filename TEXT,
            original_url TEXT,
            supabase_url TEXT,
            mime_type TEXT,
            file_size INTEGER,
            width INTEGER,
            height INTEGER,
            alt_text TEXT,
            caption TEXT,
            created_at TIMESTAMP WITH TIME ZONE,
            migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )', table_suffix);

        -- Enable RLS on all tables
        EXECUTE format('ALTER TABLE wp_posts_%s ENABLE ROW LEVEL SECURITY', table_suffix);
        EXECUTE format('ALTER TABLE wp_pages_%s ENABLE ROW LEVEL SECURITY', table_suffix);
        EXECUTE format('ALTER TABLE wp_media_%s ENABLE ROW LEVEL SECURITY', table_suffix);

        -- Create policies for wp_posts
        EXECUTE format('
          DROP POLICY IF EXISTS "wp_posts_%s_service_policy" ON wp_posts_%s;
          CREATE POLICY "wp_posts_%s_service_policy" ON wp_posts_%s
            FOR ALL TO service_role
            USING (true)
            WITH CHECK (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        EXECUTE format('
          DROP POLICY IF EXISTS "wp_posts_%s_read_policy" ON wp_posts_%s;
          CREATE POLICY "wp_posts_%s_read_policy" ON wp_posts_%s
            FOR SELECT TO authenticated
            USING (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        -- Create policies for wp_pages
        EXECUTE format('
          DROP POLICY IF EXISTS "wp_pages_%s_service_policy" ON wp_pages_%s;
          CREATE POLICY "wp_pages_%s_service_policy" ON wp_pages_%s
            FOR ALL TO service_role
            USING (true)
            WITH CHECK (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        EXECUTE format('
          DROP POLICY IF EXISTS "wp_pages_%s_read_policy" ON wp_pages_%s;
          CREATE POLICY "wp_pages_%s_read_policy" ON wp_pages_%s
            FOR SELECT TO authenticated
            USING (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        -- Create policies for wp_media
        EXECUTE format('
          DROP POLICY IF EXISTS "wp_media_%s_service_policy" ON wp_media_%s;
          CREATE POLICY "wp_media_%s_service_policy" ON wp_media_%s
            FOR ALL TO service_role
            USING (true)
            WITH CHECK (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        EXECUTE format('
          DROP POLICY IF EXISTS "wp_media_%s_read_policy" ON wp_media_%s;
          CREATE POLICY "wp_media_%s_read_policy" ON wp_media_%s
            FOR SELECT TO authenticated
            USING (true)', table_suffix, table_suffix, table_suffix, table_suffix);

        result := jsonb_build_object(
          'success', true,
          'tables_created', ARRAY[
            'wp_posts_' || table_suffix,
            'wp_pages_' || table_suffix,
            'wp_media_' || table_suffix
          ]
        );

        RETURN result;
      EXCEPTION
        WHEN OTHERS THEN
          RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM
          );
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      GRANT EXECUTE ON FUNCTION create_migration_tables(TEXT) TO service_role;
    `;

    await executeSQL(functionsSQL);
    success('Migration functions created');

    // Create summary function
    info('Creating summary function...');
    
    const summaryFunctionSQL = `
      CREATE OR REPLACE FUNCTION get_migration_summary(table_suffix TEXT)
      RETURNS JSONB AS $$
      DECLARE
        posts_count INTEGER := 0;
        pages_count INTEGER := 0;
        media_count INTEGER := 0;
        result JSONB;
      BEGIN
        -- Get posts count
        EXECUTE format('SELECT COUNT(*) FROM wp_posts_%s', table_suffix) INTO posts_count;
        
        -- Get pages count
        EXECUTE format('SELECT COUNT(*) FROM wp_pages_%s', table_suffix) INTO pages_count;
        
        -- Get media count
        EXECUTE format('SELECT COUNT(*) FROM wp_media_%s', table_suffix) INTO media_count;

        result := jsonb_build_object(
          'posts', posts_count,
          'pages', pages_count,
          'media', media_count,
          'total', posts_count + pages_count + media_count
        );

        RETURN result;
      EXCEPTION
        WHEN OTHERS THEN
          RETURN jsonb_build_object(
            'error', SQLERRM,
            'posts', 0,
            'pages', 0,
            'media', 0,
            'total', 0
          );
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      GRANT EXECUTE ON FUNCTION get_migration_summary(TEXT) TO service_role;
    `;

    await executeSQL(summaryFunctionSQL);
    success('Summary function created');

    return true;

  } catch (err) {
    error(`Database setup failed: ${err.message}`);
    return false;
  }
}

// Test the setup
async function testSetup() {
  info('Testing database setup...');

  try {
    // Test migration_jobs table
    const { error: jobsError } = await supabase
      .from('migration_jobs')
      .select('id')
      .limit(1);

    if (jobsError) {
      throw new Error(`migration_jobs table test failed: ${jobsError.message}`);
    }

    success('migration_jobs table accessible');

    // Test migration functions
    const { data: testData, error: testError } = await supabase.rpc('create_migration_tables', {
      table_suffix: 'test'
    });

    if (testError) {
      throw new Error(`Migration functions test failed: ${testError.message}`);
    }

    success('Migration functions working');

    // Clean up test tables
    if (testData?.success) {
      info('Cleaning up test tables...');
      // Test tables will be cleaned up automatically or can be ignored
    }

    return true;

  } catch (err) {
    error(`Setup test failed: ${err.message}`);
    return false;
  }
}

// Main execution
async function main() {
  log(`${colors.bold}🏗️  WordPress to Supabase Migration - Programmatic Setup${colors.reset}\n`);

  // Check required environment variables
  if (!supabaseUrl || !supabaseServiceKey) {
    error('Missing required environment variables');
    error('Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }

  if (!supabaseAccessToken) {
    warning('SUPABASE_ACCESS_TOKEN not found. Falling back to basic setup.');
    warning('For full programmatic setup, add your Supabase access token to .env.local');
    
    // Fall back to the original setup script
    const { spawn } = await import('child_process');
    const setupProcess = spawn('node', ['scripts/setup-database.js'], { stdio: 'inherit' });
    
    setupProcess.on('close', (code) => {
      process.exit(code);
    });
    
    return;
  }

  // Run programmatic setup
  const setupSuccess = await setupDatabase();
  if (!setupSuccess) {
    process.exit(1);
  }

  // Test the setup
  const testSuccess = await testSetup();
  if (!testSuccess) {
    warning('Setup completed but tests failed. The app may still work.');
  }

  success('\n🎉 Database setup completed successfully!');
  log('\nNext steps:');
  log('1. Run: npm run dev');
  log('2. Open: http://localhost:3000');
  log('3. Test the WordPress connection');
  log('4. Start your migration!');
}

main().catch(err => {
  error(`Setup failed: ${err.message}`);
  process.exit(1);
});
