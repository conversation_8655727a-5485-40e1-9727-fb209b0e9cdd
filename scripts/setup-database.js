#!/usr/bin/env node

/**
 * Database Setup Script for WordPress to Supabase Migration
 * 
 * This script sets up the necessary database functions and tables
 * in your Supabase project for the WordPress migration tool.
 * 
 * Usage: node scripts/setup-database.js
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration in .env.local');
  console.error('Required variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupDatabase() {
  console.log('🚀 Setting up Supabase database for WordPress migration...');

  try {
    // Check if migration_jobs table exists
    console.log('📝 Checking migration_jobs table...');

    const { error: jobsTableError } = await supabase
      .from('migration_jobs')
      .select('id')
      .limit(1);

    if (jobsTableError && jobsTableError.message.includes('does not exist')) {
      console.log('⚠️  migration_jobs table does not exist.');
      console.log('📋 Please create the database tables manually:');
      console.log('1. Go to your Supabase dashboard SQL Editor');
      console.log('2. Copy and paste the contents of scripts/setup-supabase.sql');
      console.log('3. Execute the SQL script');
      console.log('4. Then run this setup script again');
      return false;
    } else {
      console.log('✅ migration_jobs table exists');
    }

    // Test if migration functions exist
    console.log('🧪 Testing migration functions...');

    const { data: testData, error: testError } = await supabase.rpc('create_migration_tables', {
      table_suffix: 'test'
    });

    if (testError) {
      console.log('⚠️  Migration functions not available.');
      console.log('📋 Please create the database functions manually:');
      console.log('1. Go to your Supabase dashboard SQL Editor');
      console.log('2. Copy and paste the contents of scripts/setup-supabase.sql');
      console.log('3. Execute the SQL script');
      console.log('');
      console.log('🔄 Continuing with basic setup...');

      // Continue with basic functionality
      console.log('✅ Basic setup completed (with limited functionality)');
      console.log('\n🎉 Your WordPress to Supabase migration app is partially ready!');
      console.log('\nTo enable full functionality:');
      console.log('1. Execute the SQL script in your Supabase dashboard');
      console.log('2. Then run: npm run dev');
      console.log('3. Open: http://localhost:3000');

      return true;
    }

    // Clean up test tables
    if (testData?.success) {
      console.log('🧹 Cleaning up test tables...');
      await cleanupTestTables();
    }

    console.log('✅ Database setup completed successfully!');
    console.log('\n🎉 Your WordPress to Supabase migration app is ready to use!');
    console.log('\nNext steps:');
    console.log('1. Run: npm run dev');
    console.log('2. Open: http://localhost:3000');
    console.log('3. Test the WordPress connection');
    console.log('4. Start your migration!');

    return true;
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.log('\n📋 Manual Setup Required:');
    console.log('Please run the SQL script manually in your Supabase dashboard:');
    console.log('File: scripts/setup-supabase.sql');
    return false;
  }
}

// Create migration functions programmatically
async function createMigrationFunctions() {
  const functionSQL = `
    CREATE OR REPLACE FUNCTION create_migration_tables(table_suffix TEXT)
    RETURNS JSONB AS $$
    DECLARE
      result JSONB := '{}';
    BEGIN
      -- Create wp_posts table
      EXECUTE format('
        CREATE TABLE IF NOT EXISTS wp_posts_%s (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          content TEXT,
          excerpt TEXT,
          slug TEXT,
          status TEXT,
          type TEXT,
          author_id INTEGER,
          featured_media_id INTEGER,
          categories TEXT[],
          tags TEXT[],
          created_at TIMESTAMP WITH TIME ZONE,
          modified_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )', table_suffix);

      -- Create wp_pages table
      EXECUTE format('
        CREATE TABLE IF NOT EXISTS wp_pages_%s (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          content TEXT,
          slug TEXT,
          status TEXT,
          parent_id INTEGER,
          template TEXT,
          featured_media_id INTEGER,
          created_at TIMESTAMP WITH TIME ZONE,
          modified_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )', table_suffix);

      -- Create wp_media table
      EXECUTE format('
        CREATE TABLE IF NOT EXISTS wp_media_%s (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          filename TEXT,
          original_url TEXT,
          supabase_url TEXT,
          mime_type TEXT,
          file_size INTEGER,
          width INTEGER,
          height INTEGER,
          alt_text TEXT,
          caption TEXT,
          created_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )', table_suffix);

      -- Enable RLS on all tables
      EXECUTE format('ALTER TABLE wp_posts_%s ENABLE ROW LEVEL SECURITY', table_suffix);
      EXECUTE format('ALTER TABLE wp_pages_%s ENABLE ROW LEVEL SECURITY', table_suffix);
      EXECUTE format('ALTER TABLE wp_media_%s ENABLE ROW LEVEL SECURITY', table_suffix);

      -- Create policies for wp_posts
      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_posts_%s_service_policy" ON wp_posts_%s
          FOR ALL TO service_role
          USING (true)
          WITH CHECK (true)', table_suffix, table_suffix);

      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_posts_%s_read_policy" ON wp_posts_%s
          FOR SELECT TO authenticated
          USING (true)', table_suffix, table_suffix);

      -- Create policies for wp_pages
      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_pages_%s_service_policy" ON wp_pages_%s
          FOR ALL TO service_role
          USING (true)
          WITH CHECK (true)', table_suffix, table_suffix);

      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_pages_%s_read_policy" ON wp_pages_%s
          FOR SELECT TO authenticated
          USING (true)', table_suffix, table_suffix);

      -- Create policies for wp_media
      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_media_%s_service_policy" ON wp_media_%s
          FOR ALL TO service_role
          USING (true)
          WITH CHECK (true)', table_suffix, table_suffix);

      EXECUTE format('
        CREATE POLICY IF NOT EXISTS "wp_media_%s_read_policy" ON wp_media_%s
          FOR SELECT TO authenticated
          USING (true)', table_suffix, table_suffix);

      result := jsonb_build_object(
        'success', true,
        'tables_created', ARRAY[
          'wp_posts_' || table_suffix,
          'wp_pages_' || table_suffix,
          'wp_media_' || table_suffix
        ]
      );

      RETURN result;
    EXCEPTION
      WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'success', false,
          'error', SQLERRM
        );
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    GRANT EXECUTE ON FUNCTION create_migration_tables(TEXT) TO service_role;

    CREATE OR REPLACE FUNCTION get_migration_summary(table_suffix TEXT)
    RETURNS JSONB AS $$
    DECLARE
      posts_count INTEGER := 0;
      pages_count INTEGER := 0;
      media_count INTEGER := 0;
      result JSONB;
    BEGIN
      -- Get posts count
      EXECUTE format('SELECT COUNT(*) FROM wp_posts_%s', table_suffix) INTO posts_count;

      -- Get pages count
      EXECUTE format('SELECT COUNT(*) FROM wp_pages_%s', table_suffix) INTO pages_count;

      -- Get media count
      EXECUTE format('SELECT COUNT(*) FROM wp_media_%s', table_suffix) INTO media_count;

      result := jsonb_build_object(
        'posts', posts_count,
        'pages', pages_count,
        'media', media_count,
        'total', posts_count + pages_count + media_count
      );

      RETURN result;
    EXCEPTION
      WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'error', SQLERRM,
          'posts', 0,
          'pages', 0,
          'media', 0,
          'total', 0
        );
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    GRANT EXECUTE ON FUNCTION get_migration_summary(TEXT) TO service_role;
  `;

  try {
    // Use the Supabase client to execute the SQL
    const { error } = await supabase.rpc('exec', { sql: functionSQL });

    if (error) {
      console.warn('⚠️  Could not create functions via RPC:', error.message);
      return false;
    }

    console.log('✅ Migration functions created successfully');
    return true;
  } catch (error) {
    console.warn('⚠️  Could not create functions:', error.message);
    return false;
  }
}

// Alternative setup method
async function alternativeSetup() {
  console.log('🔄 Trying alternative setup method...');

  try {
    // Create basic migration_jobs table if it doesn't exist
    const { error: jobsError } = await supabase
      .from('migration_jobs')
      .select('id')
      .limit(1);

    if (jobsError && jobsError.message.includes('does not exist')) {
      console.log('⚠️  migration_jobs table missing. App may have limited functionality.');
    }

    console.log('✅ Alternative setup completed');
    return true;
  } catch (error) {
    console.error('❌ Alternative setup failed:', error.message);
    return false;
  }
}

// Clean up test tables
async function cleanupTestTables() {
  const testTables = ['wp_posts_test', 'wp_pages_test', 'wp_media_test'];
  for (const table of testTables) {
    try {
      // Try to drop test tables - this may fail if exec function doesn't exist
      const { error } = await supabase.rpc('exec', { sql: `DROP TABLE IF EXISTS ${table}` });
      if (error) {
        console.log(`Note: Could not clean up test table ${table} (this is normal)`);
      }
    } catch (err) {
      // Ignore cleanup errors - test tables will be cleaned up automatically
    }
  }
}

// Check if we can connect to Supabase
async function checkConnection() {
  console.log('🔗 Checking Supabase connection...');
  
  try {
    const { data, error } = await supabase.from('_supabase_migrations').select('*').limit(1);
    if (error && !error.message.includes('does not exist')) {
      throw error;
    }
    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error.message);
    console.log('\n🔧 Please check your Supabase configuration:');
    console.log('- Verify NEXT_PUBLIC_SUPABASE_URL is correct');
    console.log('- Verify SUPABASE_SERVICE_ROLE_KEY is correct');
    console.log('- Ensure your Supabase project is active');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️  WordPress to Supabase Migration - Database Setup\n');
  
  const connectionOk = await checkConnection();
  if (!connectionOk) {
    process.exit(1);
  }
  
  const setupOk = await setupDatabase();
  if (!setupOk) {
    process.exit(1);
  }
  
  console.log('\n🎯 Setup complete! Happy migrating! 🚀');
}

main().catch(console.error);
